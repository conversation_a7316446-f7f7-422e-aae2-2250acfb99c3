"""Integration tests for conversation flow."""

import pytest
import uuid
from unittest.mock import AsyncMock, patch

from travel_chatbot.conversation.state import Convers<PERSON><PERSON><PERSON>, state_manager
from travel_chatbot.conversation.graph import TravelConversationGraph


class TestConversationFlow:
    """Test end-to-end conversation flow."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.session_id = str(uuid.uuid4())
        self.conversation_graph = TravelConversationGraph()
    
    def test_conversation_state_creation(self):
        """Test conversation state creation and management."""
        state = state_manager.get_or_create_state(self.session_id)
        
        assert state.session_id == self.session_id
        assert state.current_intent is None
        assert state.conversation_stage == "greeting"
        assert len(state.messages) == 0
    
    def test_conversation_state_updates(self):
        """Test conversation state updates."""
        state = state_manager.get_or_create_state(self.session_id)
        
        # Update state
        state.current_intent = "hotel_search"
        state.conversation_stage = "collecting_info"
        state.hotel_search.location = "New York"
        
        # Update in manager
        state_manager.update_state(self.session_id, state)
        
        # Retrieve and verify
        retrieved_state = state_manager.get_or_create_state(self.session_id)
        assert retrieved_state.current_intent == "hotel_search"
        assert retrieved_state.conversation_stage == "collecting_info"
        assert retrieved_state.hotel_search.location == "New York"
    
    def test_hotel_search_state_completion(self):
        """Test hotel search state completion check."""
        state = state_manager.get_or_create_state(self.session_id)
        
        # Initially incomplete
        assert not state.hotel_search.is_complete()
        assert "location" in state.hotel_search.missing_fields()
        
        # Fill required fields
        state.hotel_search.location = "New York"
        state.hotel_search.check_in_date = "2024-12-15"
        state.hotel_search.check_out_date = "2024-12-17"
        
        # Now complete
        assert state.hotel_search.is_complete()
        assert len(state.hotel_search.missing_fields()) == 0
    
    def test_flight_search_state_completion(self):
        """Test flight search state completion check."""
        state = state_manager.get_or_create_state(self.session_id)
        
        # Initially incomplete
        assert not state.flight_search.is_complete()
        assert "origin" in state.flight_search.missing_fields()
        
        # Fill required fields
        state.flight_search.origin = "NYC"
        state.flight_search.destination = "LAX"
        state.flight_search.departure_date = "2024-12-15"
        
        # Now complete
        assert state.flight_search.is_complete()
        assert len(state.flight_search.missing_fields()) == 0
    
    @pytest.mark.asyncio
    async def test_conversation_graph_initialization(self):
        """Test conversation graph initialization."""
        graph = TravelConversationGraph()
        
        assert graph is not None
        assert hasattr(graph, 'entity_extractor')
        assert hasattr(graph, 'graph')
    
    def test_state_manager_cleanup(self):
        """Test state manager cleanup functionality."""
        # Create multiple states
        session1 = str(uuid.uuid4())
        session2 = str(uuid.uuid4())
        
        state1 = state_manager.get_or_create_state(session1)
        state2 = state_manager.get_or_create_state(session2)
        
        assert len(state_manager.get_active_sessions()) >= 2
        
        # Test cleanup (won't actually clean up recent sessions)
        cleaned = state_manager.cleanup_old_sessions(max_age_hours=0.001)  # Very short age
        
        # Verify sessions still exist (they're too recent)
        assert session1 in state_manager.get_active_sessions()
        assert session2 in state_manager.get_active_sessions()


@pytest.mark.asyncio
class TestConversationIntegration:
    """Test conversation integration with mocked external services."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.session_id = str(uuid.uuid4())
    
    @patch('travel_chatbot.core.bedrock_client.bedrock_client')
    async def test_mock_conversation_flow(self, mock_bedrock):
        """Test conversation flow with mocked Bedrock client."""
        # Mock Bedrock responses
        mock_bedrock.invoke = AsyncMock()
        mock_bedrock.invoke.return_value.content = "Hello! I'm here to help you with your travel plans."
        
        # This would test the actual conversation flow
        # For now, just verify the mock setup works
        assert mock_bedrock is not None
    
    def test_conversation_summary(self):
        """Test conversation state summary generation."""
        state = state_manager.get_or_create_state(self.session_id)
        state.current_intent = "hotel_search"
        state.hotel_search.location = "New York"
        
        summary = state.get_conversation_summary()
        
        assert self.session_id in summary
        assert "hotel_search" in summary
        assert "Hotel Search" in summary

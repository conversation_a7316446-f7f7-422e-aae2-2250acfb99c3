"""Test entity extraction functionality."""

import pytest
from datetime import datetime, timedelta

from travel_chatbot.conversation.entity_extraction import EntityExtractor


class TestEntityExtractor:
    """Test entity extraction functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.extractor = EntityExtractor()
    
    def test_process_date_yyyy_mm_dd(self):
        """Test processing YYYY-MM-DD date format."""
        result = self.extractor._process_date("2024-12-15")
        assert result == "2024-12-15"
    
    def test_process_date_mm_dd_yyyy(self):
        """Test processing MM/DD/YYYY date format."""
        result = self.extractor._process_date("12/15/2024")
        assert result == "2024-12-15"
    
    def test_process_date_relative_today(self):
        """Test processing relative date 'today'."""
        result = self.extractor._process_date("today")
        expected = datetime.now().strftime('%Y-%m-%d')
        assert result == expected
    
    def test_process_date_relative_tomorrow(self):
        """Test processing relative date 'tomorrow'."""
        result = self.extractor._process_date("tomorrow")
        expected = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        assert result == expected
    
    def test_process_date_weekday(self):
        """Test processing weekday names."""
        result = self.extractor._process_date("next monday")
        assert result is not None
        assert len(result) == 10  # YYYY-MM-DD format
        assert result.count('-') == 2
    
    def test_process_date_invalid(self):
        """Test processing invalid date strings."""
        result = self.extractor._process_date("invalid date")
        assert result is None
    
    def test_post_process_entities_hotel(self):
        """Test post-processing hotel search entities."""
        raw_entities = {
            "location": "New York",
            "check_in_date": "2024-12-15",
            "check_out_date": "2024-12-17",
            "guests": "2",
            "budget_max": "$300",
            "amenities": "pool, gym, wifi"
        }
        
        processed = self.extractor._post_process_entities(raw_entities, "hotel_search")
        
        assert processed["location"] == "New York"
        assert processed["check_in_date"] == "2024-12-15"
        assert processed["check_out_date"] == "2024-12-17"
        assert processed["guests"] == 2
        assert processed["budget_max"] == 300.0
        assert "pool" in processed["amenities"]
        assert "gym" in processed["amenities"]
        assert "wifi" in processed["amenities"]
    
    def test_post_process_entities_flight(self):
        """Test post-processing flight search entities."""
        raw_entities = {
            "origin": "NYC",
            "destination": "LAX",
            "departure_date": "2024-12-15",
            "passengers": "1",
            "cabin_class": "Business Class",
            "budget_max": "1000"
        }
        
        processed = self.extractor._post_process_entities(raw_entities, "flight_search")
        
        assert processed["origin"] == "NYC"
        assert processed["destination"] == "LAX"
        assert processed["departure_date"] == "2024-12-15"
        assert processed["passengers"] == 1
        assert processed["cabin_class"] == "business_class"
        assert processed["budget_max"] == 1000.0
    
    def test_validate_entities_hotel_valid(self):
        """Test validation of valid hotel entities."""
        entities = {
            "location": "New York",
            "check_in_date": "2024-12-15",
            "check_out_date": "2024-12-17",
            "guests": 2,
            "rooms": 1
        }
        
        errors = self.extractor.validate_entities(entities, "hotel_search")
        assert len(errors) == 0
    
    def test_validate_entities_hotel_invalid_dates(self):
        """Test validation of hotel entities with invalid dates."""
        entities = {
            "location": "New York",
            "check_in_date": "2024-12-17",
            "check_out_date": "2024-12-15",  # Before check-in
            "guests": 2
        }
        
        errors = self.extractor.validate_entities(entities, "hotel_search")
        assert "dates" in errors
    
    def test_validate_entities_flight_valid(self):
        """Test validation of valid flight entities."""
        future_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
        entities = {
            "origin": "NYC",
            "destination": "LAX",
            "departure_date": future_date,
            "passengers": 2
        }
        
        errors = self.extractor.validate_entities(entities, "flight_search")
        assert len(errors) == 0
    
    def test_validate_entities_flight_past_date(self):
        """Test validation of flight entities with past departure date."""
        past_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        entities = {
            "origin": "NYC",
            "destination": "LAX",
            "departure_date": past_date,
            "passengers": 1
        }
        
        errors = self.extractor.validate_entities(entities, "flight_search")
        assert "departure_date" in errors

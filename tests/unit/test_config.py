"""Test configuration management."""

import pytest
import os
from unittest.mock import patch

from travel_chatbot.core.config import Config, BedrockConfig, TravelAPIConfig


class TestConfig:
    """Test configuration classes."""
    
    def test_bedrock_config_defaults(self):
        """Test BedrockConfig default values."""
        config = BedrockConfig()
        
        assert config.region == "us-east-1"
        assert config.model_id == "anthropic.claude-3-5-sonnet-20241022-v2:0"
        assert config.max_tokens == 4096
        assert config.temperature == 0.7
    
    def test_bedrock_config_from_env(self):
        """Test BedrockConfig with environment variables."""
        with patch.dict(os.environ, {
            'AWS_REGION': 'us-west-2',
            'BEDROCK_MODEL_ID': 'test-model',
            'BEDROCK_MAX_TOKENS': '2048',
            'BEDROCK_TEMPERATURE': '0.5'
        }):
            config = BedrockConfig()
            
            assert config.region == "us-west-2"
            assert config.model_id == "test-model"
            assert config.max_tokens == 2048
            assert config.temperature == 0.5
    
    def test_travel_api_config_defaults(self):
        """Test TravelAPIConfig default values."""
        config = TravelAPIConfig()
        
        assert "api.example.com" in config.hotel_search_url
        assert "api.example.com" in config.flight_search_url
        assert config.api_key is None
    
    def test_config_validation_missing_fields(self):
        """Test configuration validation with missing fields."""
        config = Config()
        
        # Should fail validation with default example URLs
        assert not config.validate()
    
    def test_config_validation_valid_config(self):
        """Test configuration validation with valid config."""
        with patch.dict(os.environ, {
            'HOTEL_SEARCH_API_URL': 'https://real-api.com/hotels',
            'FLIGHT_SEARCH_API_URL': 'https://real-api.com/flights',
            'TRAVEL_API_KEY': 'real-api-key'
        }):
            config = Config()
            
            # Should pass validation with real URLs
            assert config.validate()


class TestConfigIntegration:
    """Test configuration integration."""
    
    def test_config_loading(self):
        """Test that config loads without errors."""
        from travel_chatbot.core.config import config
        
        assert config is not None
        assert hasattr(config, 'bedrock')
        assert hasattr(config, 'travel_api')
        assert hasattr(config, 'mcp')
        assert hasattr(config, 'app')

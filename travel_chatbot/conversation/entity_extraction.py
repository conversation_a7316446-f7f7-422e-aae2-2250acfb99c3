"""Entity extraction for travel conversations."""

import json
import logging
import re
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from langchain_core.messages import SystemMessage, HumanMessage

from .prompts import get_entity_extraction_prompt
from ..core.bedrock_client import bedrock_client

logger = logging.getLogger(__name__)


class EntityExtractor:
    """Extract entities from user messages for travel searches."""
    
    def __init__(self):
        self.date_patterns = {
            r'(\d{4}-\d{2}-\d{2})': '%Y-%m-%d',  # YYYY-MM-DD
            r'(\d{1,2}/\d{1,2}/\d{4})': '%m/%d/%Y',  # MM/DD/YYYY
            r'(\d{1,2}-\d{1,2}-\d{4})': '%m-%d-%Y',  # MM-DD-YYYY
        }
        
        self.relative_dates = {
            'today': 0,
            'tomorrow': 1,
            'next week': 7,
            'next month': 30,
        }
    
    async def extract_entities(self, messages: List[str], intent: str) -> Dict[str, Any]:
        """Extract entities from user messages based on intent."""
        try:
            # Combine messages into context
            context = " ".join(messages)
            
            # Use LLM for entity extraction
            llm_entities = await self._extract_with_llm(context, intent)
            
            # Post-process and validate entities
            processed_entities = self._post_process_entities(llm_entities, intent)
            
            return processed_entities
            
        except Exception as e:
            logger.error(f"Entity extraction error: {str(e)}")
            return {}
    
    async def _extract_with_llm(self, context: str, intent: str) -> Dict[str, Any]:
        """Use LLM to extract entities from context."""
        extraction_prompt = get_entity_extraction_prompt(intent)
        
        if not extraction_prompt:
            return {}
        
        messages = [
            SystemMessage(content=extraction_prompt),
            HumanMessage(content=f"User messages: {context}")
        ]
        
        try:
            response = await bedrock_client.invoke(messages)
            
            # Try to parse JSON response
            response_text = response.content.strip()
            
            # Clean up response if it contains markdown code blocks
            if '```json' in response_text:
                response_text = response_text.split('```json')[1].split('```')[0].strip()
            elif '```' in response_text:
                response_text = response_text.split('```')[1].strip()
            
            entities = json.loads(response_text)
            return entities if isinstance(entities, dict) else {}
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LLM entity extraction response: {str(e)}")
            return {}
        except Exception as e:
            logger.error(f"LLM entity extraction error: {str(e)}")
            return {}
    
    def _post_process_entities(self, entities: Dict[str, Any], intent: str) -> Dict[str, Any]:
        """Post-process and validate extracted entities."""
        processed = {}
        
        for key, value in entities.items():
            if value is None or value == "":
                continue
            
            # Process dates
            if key.endswith('_date') and isinstance(value, str):
                processed_date = self._process_date(value)
                if processed_date:
                    processed[key] = processed_date
            
            # Process numeric values
            elif key in ['guests', 'rooms', 'passengers'] and value:
                try:
                    processed[key] = int(value)
                except (ValueError, TypeError):
                    # Try to extract number from string
                    if isinstance(value, str):
                        numbers = re.findall(r'\d+', value)
                        if numbers:
                            processed[key] = int(numbers[0])
            
            # Process budget values
            elif key in ['budget_min', 'budget_max', 'budget_max'] and value:
                try:
                    # Remove currency symbols and convert to float
                    clean_value = re.sub(r'[^\d.]', '', str(value))
                    if clean_value:
                        processed[key] = float(clean_value)
                except (ValueError, TypeError):
                    pass
            
            # Process lists (amenities)
            elif key == 'amenities' and value:
                if isinstance(value, list):
                    processed[key] = [str(item).lower() for item in value if item]
                elif isinstance(value, str):
                    # Split by common delimiters
                    amenities = re.split(r'[,;]', value)
                    processed[key] = [item.strip().lower() for item in amenities if item.strip()]
            
            # Process cabin class and trip type
            elif key in ['cabin_class', 'trip_type'] and isinstance(value, str):
                processed[key] = value.lower().replace(' ', '_')
            
            # Process other string values
            elif isinstance(value, str):
                processed[key] = value.strip()
            
            else:
                processed[key] = value
        
        return processed
    
    def _process_date(self, date_str: str) -> Optional[str]:
        """Process and normalize date strings."""
        if not date_str:
            return None
        
        date_str = date_str.strip().lower()
        
        # Check if already in YYYY-MM-DD format
        if re.match(r'^\d{4}-\d{2}-\d{2}$', date_str):
            return date_str
        
        # Try to parse with different patterns
        for pattern, format_str in self.date_patterns.items():
            match = re.search(pattern, date_str)
            if match:
                try:
                    date_obj = datetime.strptime(match.group(1), format_str)
                    return date_obj.strftime('%Y-%m-%d')
                except ValueError:
                    continue
        
        # Handle relative dates
        for relative, days_offset in self.relative_dates.items():
            if relative in date_str:
                target_date = datetime.now() + timedelta(days=days_offset)
                return target_date.strftime('%Y-%m-%d')
        
        # Handle day names (next Monday, this Friday, etc.)
        weekdays = {
            'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
            'friday': 4, 'saturday': 5, 'sunday': 6
        }
        
        for day_name, day_num in weekdays.items():
            if day_name in date_str:
                today = datetime.now()
                days_ahead = day_num - today.weekday()
                
                if 'next' in date_str:
                    days_ahead += 7
                elif days_ahead <= 0:  # This week
                    days_ahead += 7
                
                target_date = today + timedelta(days=days_ahead)
                return target_date.strftime('%Y-%m-%d')
        
        # Try to parse month names
        month_names = {
            'january': 1, 'february': 2, 'march': 3, 'april': 4,
            'may': 5, 'june': 6, 'july': 7, 'august': 8,
            'september': 9, 'october': 10, 'november': 11, 'december': 12
        }
        
        for month_name, month_num in month_names.items():
            if month_name in date_str:
                # Look for day number
                day_match = re.search(r'\b(\d{1,2})\b', date_str)
                if day_match:
                    try:
                        day = int(day_match.group(1))
                        year = datetime.now().year
                        
                        # If the month has passed this year, use next year
                        if month_num < datetime.now().month:
                            year += 1
                        
                        date_obj = datetime(year, month_num, day)
                        return date_obj.strftime('%Y-%m-%d')
                    except ValueError:
                        continue
        
        logger.warning(f"Could not parse date: {date_str}")
        return None
    
    def validate_entities(self, entities: Dict[str, Any], intent: str) -> Dict[str, List[str]]:
        """Validate extracted entities and return validation errors."""
        errors = {}
        
        if intent == "hotel_search":
            # Validate hotel search entities
            if 'check_in_date' in entities and 'check_out_date' in entities:
                try:
                    check_in = datetime.strptime(entities['check_in_date'], '%Y-%m-%d')
                    check_out = datetime.strptime(entities['check_out_date'], '%Y-%m-%d')
                    
                    if check_out <= check_in:
                        errors['dates'] = ['Check-out date must be after check-in date']
                    
                    if check_in < datetime.now().date():
                        errors['check_in_date'] = ['Check-in date cannot be in the past']
                        
                except ValueError:
                    errors['dates'] = ['Invalid date format']
            
            if 'guests' in entities:
                if entities['guests'] < 1 or entities['guests'] > 10:
                    errors['guests'] = ['Number of guests must be between 1 and 10']
            
            if 'rooms' in entities:
                if entities['rooms'] < 1 or entities['rooms'] > 5:
                    errors['rooms'] = ['Number of rooms must be between 1 and 5']
        
        elif intent == "flight_search":
            # Validate flight search entities
            if 'departure_date' in entities:
                try:
                    departure = datetime.strptime(entities['departure_date'], '%Y-%m-%d')
                    if departure.date() < datetime.now().date():
                        errors['departure_date'] = ['Departure date cannot be in the past']
                except ValueError:
                    errors['departure_date'] = ['Invalid departure date format']
            
            if 'return_date' in entities and 'departure_date' in entities:
                try:
                    departure = datetime.strptime(entities['departure_date'], '%Y-%m-%d')
                    return_date = datetime.strptime(entities['return_date'], '%Y-%m-%d')
                    
                    if return_date <= departure:
                        errors['return_date'] = ['Return date must be after departure date']
                        
                except ValueError:
                    errors['dates'] = ['Invalid date format']
            
            if 'passengers' in entities:
                if entities['passengers'] < 1 or entities['passengers'] > 9:
                    errors['passengers'] = ['Number of passengers must be between 1 and 9']
        
        return errors

"""Conversation state management for travel chatbot."""

from typing import Dict, Any, List, Optional, Literal
from pydantic import BaseModel, Field
from langchain_core.messages import BaseMessage
from datetime import datetime


class UserProfile(BaseModel):
    """User profile information."""
    
    name: Optional[str] = None
    preferences: Dict[str, Any] = Field(default_factory=dict)
    travel_history: List[Dict[str, Any]] = Field(default_factory=list)


class HotelSearchState(BaseModel):
    """State for hotel search conversation."""
    
    location: Optional[str] = None
    check_in_date: Optional[str] = None
    check_out_date: Optional[str] = None
    guests: Optional[int] = None
    rooms: Optional[int] = None
    budget_min: Optional[float] = None
    budget_max: Optional[float] = None
    amenities: List[str] = Field(default_factory=list)
    search_results: Optional[Dict[str, Any]] = None
    
    def is_complete(self) -> bool:
        """Check if all required fields are filled."""
        required_fields = [self.location, self.check_in_date, self.check_out_date]
        return all(field is not None for field in required_fields)
    
    def missing_fields(self) -> List[str]:
        """Get list of missing required fields."""
        missing = []
        if not self.location:
            missing.append("location")
        if not self.check_in_date:
            missing.append("check_in_date")
        if not self.check_out_date:
            missing.append("check_out_date")
        return missing


class FlightSearchState(BaseModel):
    """State for flight search conversation."""
    
    origin: Optional[str] = None
    destination: Optional[str] = None
    departure_date: Optional[str] = None
    return_date: Optional[str] = None
    passengers: Optional[int] = None
    cabin_class: Optional[str] = None
    trip_type: Optional[str] = None
    budget_max: Optional[float] = None
    search_results: Optional[Dict[str, Any]] = None
    
    def is_complete(self) -> bool:
        """Check if all required fields are filled."""
        required_fields = [self.origin, self.destination, self.departure_date]
        return all(field is not None for field in required_fields)
    
    def missing_fields(self) -> List[str]:
        """Get list of missing required fields."""
        missing = []
        if not self.origin:
            missing.append("origin")
        if not self.destination:
            missing.append("destination")
        if not self.departure_date:
            missing.append("departure_date")
        return missing


class ConversationState(BaseModel):
    """Main conversation state."""
    
    # Conversation metadata
    session_id: str
    user_id: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    last_updated: datetime = Field(default_factory=datetime.now)
    
    # User information
    user_profile: UserProfile = Field(default_factory=UserProfile)
    
    # Conversation flow
    current_intent: Optional[Literal["hotel_search", "flight_search", "general"]] = None
    conversation_stage: str = "greeting"  # greeting, collecting_info, searching, presenting_results, follow_up
    
    # Search states
    hotel_search: HotelSearchState = Field(default_factory=HotelSearchState)
    flight_search: FlightSearchState = Field(default_factory=FlightSearchState)
    
    # Message history
    messages: List[BaseMessage] = Field(default_factory=list)
    
    # Context and memory
    context: Dict[str, Any] = Field(default_factory=dict)
    extracted_entities: Dict[str, Any] = Field(default_factory=dict)
    
    def update_timestamp(self):
        """Update the last_updated timestamp."""
        self.last_updated = datetime.now()
    
    def add_message(self, message: BaseMessage):
        """Add a message to the conversation history."""
        self.messages.append(message)
        self.update_timestamp()
    
    def get_active_search_state(self) -> Optional[HotelSearchState | FlightSearchState]:
        """Get the currently active search state."""
        if self.current_intent == "hotel_search":
            return self.hotel_search
        elif self.current_intent == "flight_search":
            return self.flight_search
        return None
    
    def reset_search_state(self, intent: Literal["hotel_search", "flight_search"]):
        """Reset the search state for a specific intent."""
        if intent == "hotel_search":
            self.hotel_search = HotelSearchState()
        elif intent == "flight_search":
            self.flight_search = FlightSearchState()
    
    def get_conversation_summary(self) -> str:
        """Get a summary of the current conversation state."""
        summary = f"Session: {self.session_id}\n"
        summary += f"Intent: {self.current_intent}\n"
        summary += f"Stage: {self.conversation_stage}\n"
        
        if self.current_intent == "hotel_search":
            search_state = self.hotel_search
            summary += f"Hotel Search - Complete: {search_state.is_complete()}\n"
            if not search_state.is_complete():
                summary += f"Missing: {', '.join(search_state.missing_fields())}\n"
        
        elif self.current_intent == "flight_search":
            search_state = self.flight_search
            summary += f"Flight Search - Complete: {search_state.is_complete()}\n"
            if not search_state.is_complete():
                summary += f"Missing: {', '.join(search_state.missing_fields())}\n"
        
        return summary


class ConversationStateManager:
    """Manager for conversation states."""
    
    def __init__(self):
        self._states: Dict[str, ConversationState] = {}
    
    def get_or_create_state(self, session_id: str, user_id: Optional[str] = None) -> ConversationState:
        """Get existing state or create new one."""
        if session_id not in self._states:
            self._states[session_id] = ConversationState(
                session_id=session_id,
                user_id=user_id
            )
        return self._states[session_id]
    
    def update_state(self, session_id: str, state: ConversationState):
        """Update conversation state."""
        state.update_timestamp()
        self._states[session_id] = state
    
    def delete_state(self, session_id: str):
        """Delete conversation state."""
        if session_id in self._states:
            del self._states[session_id]
    
    def get_active_sessions(self) -> List[str]:
        """Get list of active session IDs."""
        return list(self._states.keys())
    
    def cleanup_old_sessions(self, max_age_hours: int = 24):
        """Clean up old conversation sessions."""
        cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)
        
        sessions_to_delete = []
        for session_id, state in self._states.items():
            if state.last_updated.timestamp() < cutoff_time:
                sessions_to_delete.append(session_id)
        
        for session_id in sessions_to_delete:
            del self._states[session_id]
        
        return len(sessions_to_delete)


# Global state manager
state_manager = ConversationStateManager()

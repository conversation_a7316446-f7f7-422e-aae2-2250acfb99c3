"""LangGraph conversation flow implementation."""

import logging
from typing import Dict, Any, List, Optional
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolExecutor

from .state import ConversationState, state_manager
from .prompts import SYSTEM_PROMPTS, get_prompt_for_stage
from .entity_extraction import EntityExtractor
from ..core.bedrock_client import bedrock_client
from ..mcp_server.tools import travel_api_client, HotelSearchParams, FlightSearchParams

logger = logging.getLogger(__name__)


class TravelConversationGraph:
    """LangGraph-based conversation flow for travel chatbot."""
    
    def __init__(self):
        self.entity_extractor = EntityExtractor()
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """Build the conversation flow graph."""
        
        # Define the graph
        workflow = StateGraph(ConversationState)
        
        # Add nodes
        workflow.add_node("greeting", self._greeting_node)
        workflow.add_node("intent_detection", self._intent_detection_node)
        workflow.add_node("entity_extraction", self._entity_extraction_node)
        workflow.add_node("information_collection", self._information_collection_node)
        workflow.add_node("search_execution", self._search_execution_node)
        workflow.add_node("result_presentation", self._result_presentation_node)
        workflow.add_node("follow_up", self._follow_up_node)
        
        # Define edges
        workflow.set_entry_point("greeting")
        
        workflow.add_edge("greeting", "intent_detection")
        workflow.add_edge("intent_detection", "entity_extraction")
        workflow.add_edge("entity_extraction", "information_collection")
        
        # Conditional edge from information_collection
        workflow.add_conditional_edges(
            "information_collection",
            self._should_search,
            {
                "search": "search_execution",
                "collect_more": "information_collection",
                "clarify": "intent_detection"
            }
        )
        
        workflow.add_edge("search_execution", "result_presentation")
        workflow.add_edge("result_presentation", "follow_up")
        
        # Conditional edge from follow_up
        workflow.add_conditional_edges(
            "follow_up",
            self._should_continue,
            {
                "new_search": "intent_detection",
                "refine_search": "information_collection",
                "end": END
            }
        )
        
        return workflow.compile()
    
    async def _greeting_node(self, state: ConversationState) -> ConversationState:
        """Handle initial greeting and setup."""
        if state.conversation_stage == "greeting":
            system_prompt = SYSTEM_PROMPTS["greeting"]
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content="Hello")
            ]
            
            response = await bedrock_client.invoke(messages)
            state.add_message(response)
            state.conversation_stage = "intent_detection"
        
        return state
    
    async def _intent_detection_node(self, state: ConversationState) -> ConversationState:
        """Detect user intent (hotel search, flight search, or general)."""
        if not state.messages:
            return state
        
        last_user_message = None
        for msg in reversed(state.messages):
            if isinstance(msg, HumanMessage):
                last_user_message = msg
                break
        
        if not last_user_message:
            return state
        
        # Use LLM to detect intent
        intent_prompt = get_prompt_for_stage("intent_detection")
        messages = [
            SystemMessage(content=intent_prompt),
            HumanMessage(content=f"User message: {last_user_message.content}")
        ]
        
        response = await bedrock_client.invoke(messages)
        intent_text = response.content.lower()
        
        # Parse intent from response
        if "hotel" in intent_text:
            state.current_intent = "hotel_search"
        elif "flight" in intent_text:
            state.current_intent = "flight_search"
        else:
            state.current_intent = "general"
        
        state.conversation_stage = "entity_extraction"
        logger.info(f"Detected intent: {state.current_intent}")
        
        return state
    
    async def _entity_extraction_node(self, state: ConversationState) -> ConversationState:
        """Extract entities from user messages."""
        if not state.messages:
            return state
        
        # Get recent user messages for entity extraction
        recent_messages = []
        for msg in reversed(state.messages[-5:]):  # Last 5 messages
            if isinstance(msg, HumanMessage):
                recent_messages.append(msg.content)
        
        if recent_messages:
            # Extract entities based on current intent
            entities = await self.entity_extractor.extract_entities(
                recent_messages, 
                state.current_intent
            )
            
            # Update search state with extracted entities
            if state.current_intent == "hotel_search":
                self._update_hotel_search_state(state, entities)
            elif state.current_intent == "flight_search":
                self._update_flight_search_state(state, entities)
            
            state.extracted_entities.update(entities)
        
        state.conversation_stage = "collecting_info"
        return state
    
    async def _information_collection_node(self, state: ConversationState) -> ConversationState:
        """Collect missing information from user."""
        search_state = state.get_active_search_state()
        
        if not search_state or search_state.is_complete():
            return state
        
        # Generate prompt to ask for missing information
        missing_fields = search_state.missing_fields()
        collection_prompt = get_prompt_for_stage("information_collection")
        
        context = {
            "intent": state.current_intent,
            "missing_fields": missing_fields,
            "current_state": search_state.dict()
        }
        
        messages = [
            SystemMessage(content=collection_prompt.format(**context)),
            *state.messages[-3:]  # Include recent conversation context
        ]
        
        response = await bedrock_client.invoke(messages)
        state.add_message(response)
        
        return state
    
    async def _search_execution_node(self, state: ConversationState) -> ConversationState:
        """Execute the search using MCP tools."""
        search_state = state.get_active_search_state()
        
        if not search_state or not search_state.is_complete():
            return state
        
        try:
            if state.current_intent == "hotel_search":
                params = HotelSearchParams(**search_state.dict())
                results = await travel_api_client.search_hotels(params)
                state.hotel_search.search_results = results
            
            elif state.current_intent == "flight_search":
                params = FlightSearchParams(**search_state.dict())
                results = await travel_api_client.search_flights(params)
                state.flight_search.search_results = results
            
            state.conversation_stage = "presenting_results"
            logger.info(f"Search executed for {state.current_intent}")
            
        except Exception as e:
            logger.error(f"Search execution failed: {str(e)}")
            # Add error message to conversation
            error_msg = AIMessage(content=f"I apologize, but I encountered an error while searching. Please try again later.")
            state.add_message(error_msg)
        
        return state
    
    async def _result_presentation_node(self, state: ConversationState) -> ConversationState:
        """Present search results to user."""
        search_state = state.get_active_search_state()
        
        if not search_state or not search_state.search_results:
            return state
        
        # Format results using LLM
        results_prompt = get_prompt_for_stage("result_presentation")
        
        context = {
            "intent": state.current_intent,
            "search_params": search_state.dict(),
            "results": search_state.search_results
        }
        
        messages = [
            SystemMessage(content=results_prompt.format(**context))
        ]
        
        response = await bedrock_client.invoke(messages)
        state.add_message(response)
        state.conversation_stage = "follow_up"
        
        return state
    
    async def _follow_up_node(self, state: ConversationState) -> ConversationState:
        """Handle follow-up questions and next steps."""
        follow_up_prompt = get_prompt_for_stage("follow_up")
        
        messages = [
            SystemMessage(content=follow_up_prompt),
            *state.messages[-2:]  # Include recent context
        ]
        
        response = await bedrock_client.invoke(messages)
        state.add_message(response)
        
        return state
    
    def _should_search(self, state: ConversationState) -> str:
        """Determine if we should execute search or collect more info."""
        search_state = state.get_active_search_state()
        
        if not search_state:
            return "clarify"
        
        if search_state.is_complete():
            return "search"
        else:
            return "collect_more"
    
    def _should_continue(self, state: ConversationState) -> str:
        """Determine next action after presenting results."""
        # This would typically analyze the last user message
        # For now, we'll end the conversation
        return "end"
    
    def _update_hotel_search_state(self, state: ConversationState, entities: Dict[str, Any]):
        """Update hotel search state with extracted entities."""
        hotel_state = state.hotel_search
        
        if "location" in entities:
            hotel_state.location = entities["location"]
        if "check_in_date" in entities:
            hotel_state.check_in_date = entities["check_in_date"]
        if "check_out_date" in entities:
            hotel_state.check_out_date = entities["check_out_date"]
        if "guests" in entities:
            hotel_state.guests = entities["guests"]
        if "rooms" in entities:
            hotel_state.rooms = entities["rooms"]
        if "budget_min" in entities:
            hotel_state.budget_min = entities["budget_min"]
        if "budget_max" in entities:
            hotel_state.budget_max = entities["budget_max"]
        if "amenities" in entities:
            hotel_state.amenities.extend(entities["amenities"])
    
    def _update_flight_search_state(self, state: ConversationState, entities: Dict[str, Any]):
        """Update flight search state with extracted entities."""
        flight_state = state.flight_search
        
        if "origin" in entities:
            flight_state.origin = entities["origin"]
        if "destination" in entities:
            flight_state.destination = entities["destination"]
        if "departure_date" in entities:
            flight_state.departure_date = entities["departure_date"]
        if "return_date" in entities:
            flight_state.return_date = entities["return_date"]
        if "passengers" in entities:
            flight_state.passengers = entities["passengers"]
        if "cabin_class" in entities:
            flight_state.cabin_class = entities["cabin_class"]
        if "trip_type" in entities:
            flight_state.trip_type = entities["trip_type"]
        if "budget_max" in entities:
            flight_state.budget_max = entities["budget_max"]
    
    async def process_message(self, session_id: str, user_message: str, user_id: Optional[str] = None) -> str:
        """Process a user message through the conversation graph."""
        # Get or create conversation state
        state = state_manager.get_or_create_state(session_id, user_id)
        
        # Add user message to state
        state.add_message(HumanMessage(content=user_message))
        
        # Run through the graph
        try:
            result_state = await self.graph.ainvoke(state)
            
            # Update state manager
            state_manager.update_state(session_id, result_state)
            
            # Return the last AI message
            for msg in reversed(result_state.messages):
                if isinstance(msg, AIMessage):
                    return msg.content
            
            return "I'm sorry, I didn't understand that. Could you please rephrase?"
            
        except Exception as e:
            logger.error(f"Conversation graph error: {str(e)}")
            return "I apologize, but I encountered an error. Please try again."


# Global conversation graph instance
conversation_graph = TravelConversationGraph()

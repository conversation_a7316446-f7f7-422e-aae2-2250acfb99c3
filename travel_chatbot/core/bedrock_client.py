"""AWS Bedrock client for Claude Sonnet 4 integration."""

import json
import logging
from typing import Dict, Any, Optional, AsyncGenerator
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from langchain_aws import ChatBedrock
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_core.callbacks import CallbackManagerFor<PERSON><PERSON>un

from .config import config

logger = logging.getLogger(__name__)


class BedrockClientError(Exception):
    """Custom exception for Bedrock client errors."""
    pass


class BedrockClient:
    """AWS Bedrock client for Claude Sonnet 4."""
    
    def __init__(self):
        self.config = config.bedrock
        self._client = None
        self._chat_model = None
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """Initialize the Bedrock client and ChatBedrock model."""
        try:
            # Create boto3 session with credentials
            session_kwargs = {"region_name": self.config.region}
            
            if self.config.access_key_id and self.config.secret_access_key:
                session_kwargs.update({
                    "aws_access_key_id": self.config.access_key_id,
                    "aws_secret_access_key": self.config.secret_access_key
                })
            
            session = boto3.Session(**session_kwargs)
            self._client = session.client("bedrock-runtime")
            
            # Initialize ChatBedrock model
            self._chat_model = ChatBedrock(
                client=self._client,
                model_id=self.config.model_id,
                model_kwargs={
                    "max_tokens": self.config.max_tokens,
                    "temperature": self.config.temperature,
                    "top_p": 0.9,
                    "stop_sequences": []
                },
                streaming=True
            )
            
            logger.info(f"Bedrock client initialized with model: {self.config.model_id}")
            
        except NoCredentialsError:
            raise BedrockClientError(
                "AWS credentials not found. Please configure your AWS credentials."
            )
        except Exception as e:
            raise BedrockClientError(f"Failed to initialize Bedrock client: {str(e)}")
    
    @property
    def chat_model(self) -> ChatBedrock:
        """Get the ChatBedrock model instance."""
        if self._chat_model is None:
            raise BedrockClientError("Bedrock client not initialized")
        return self._chat_model
    
    async def invoke(self, messages: list[BaseMessage]) -> AIMessage:
        """Invoke the model with a list of messages."""
        try:
            response = await self._chat_model.ainvoke(messages)
            return response
        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "Unknown")
            error_message = e.response.get("Error", {}).get("Message", str(e))
            raise BedrockClientError(f"Bedrock API error ({error_code}): {error_message}")
        except Exception as e:
            raise BedrockClientError(f"Unexpected error during model invocation: {str(e)}")
    
    async def stream(self, messages: list[BaseMessage]) -> AsyncGenerator[str, None]:
        """Stream the model response."""
        try:
            async for chunk in self._chat_model.astream(messages):
                if hasattr(chunk, 'content') and chunk.content:
                    yield chunk.content
        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code", "Unknown")
            error_message = e.response.get("Error", {}).get("Message", str(e))
            raise BedrockClientError(f"Bedrock API error ({error_code}): {error_message}")
        except Exception as e:
            raise BedrockClientError(f"Unexpected error during model streaming: {str(e)}")
    
    def test_connection(self) -> bool:
        """Test the Bedrock connection."""
        try:
            test_message = [HumanMessage(content="Hello, can you respond with 'Connection successful'?")]
            response = self._chat_model.invoke(test_message)
            logger.info("Bedrock connection test successful")
            return True
        except Exception as e:
            logger.error(f"Bedrock connection test failed: {str(e)}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model configuration."""
        return {
            "model_id": self.config.model_id,
            "region": self.config.region,
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature,
            "client_initialized": self._client is not None,
            "chat_model_initialized": self._chat_model is not None
        }


# Global Bedrock client instance
bedrock_client = BedrockClient()

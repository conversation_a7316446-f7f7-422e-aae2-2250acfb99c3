"""Configuration management for the travel chatbot."""

import os
from typing import Optional
from pydantic import BaseSettings, <PERSON>
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class BedrockConfig(BaseSettings):
    """AWS Bedrock configuration."""
    
    region: str = Field(default="us-east-1", env="AWS_REGION")
    access_key_id: Optional[str] = Field(default=None, env="AWS_ACCESS_KEY_ID")
    secret_access_key: Optional[str] = Field(default=None, env="AWS_SECRET_ACCESS_KEY")
    model_id: str = Field(
        default="anthropic.claude-3-5-sonnet-20241022-v2:0", 
        env="BEDROCK_MODEL_ID"
    )
    max_tokens: int = Field(default=4096, env="BEDROCK_MAX_TOKENS")
    temperature: float = Field(default=0.7, env="BEDROCK_TEMPERATURE")


class TravelAPIConfig(BaseSettings):
    """Travel API configuration."""
    
    hotel_search_url: str = Field(
        default="https://api.example.com/hotels/search",
        env="HOTEL_SEARCH_API_URL"
    )
    flight_search_url: str = Field(
        default="https://api.example.com/flights/search",
        env="FLIGHT_SEARCH_API_URL"
    )
    api_key: Optional[str] = Field(default=None, env="TRAVEL_API_KEY")


class MCPConfig(BaseSettings):
    """MCP Server configuration."""
    
    host: str = Field(default="localhost", env="MCP_SERVER_HOST")
    port: int = Field(default=8000, env="MCP_SERVER_PORT")


class AppConfig(BaseSettings):
    """Main application configuration."""
    
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    debug: bool = Field(default=False, env="DEBUG")
    
    # LangSmith configuration (optional)
    langchain_tracing: bool = Field(default=False, env="LANGCHAIN_TRACING_V2")
    langchain_endpoint: Optional[str] = Field(default=None, env="LANGCHAIN_ENDPOINT")
    langchain_api_key: Optional[str] = Field(default=None, env="LANGCHAIN_API_KEY")
    langchain_project: str = Field(default="travel-chatbot", env="LANGCHAIN_PROJECT")


class Config:
    """Main configuration class that combines all config sections."""
    
    def __init__(self):
        self.bedrock = BedrockConfig()
        self.travel_api = TravelAPIConfig()
        self.mcp = MCPConfig()
        self.app = AppConfig()
    
    def validate(self) -> bool:
        """Validate that required configuration is present."""
        required_fields = [
            (self.bedrock.model_id, "BEDROCK_MODEL_ID"),
            (self.travel_api.hotel_search_url, "HOTEL_SEARCH_API_URL"),
            (self.travel_api.flight_search_url, "FLIGHT_SEARCH_API_URL"),
        ]
        
        missing_fields = []
        for value, field_name in required_fields:
            if not value or value.startswith("your_") or value == "https://api.example.com":
                missing_fields.append(field_name)
        
        if missing_fields:
            print(f"Missing required configuration: {', '.join(missing_fields)}")
            print("Please check your .env file and ensure all required fields are set.")
            return False
        
        return True


# Global configuration instance
config = Config()

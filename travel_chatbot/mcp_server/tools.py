"""MCP tools for hotel and flight search."""

import json
import logging
from datetime import datetime, date
from typing import Dict, Any, List, Optional
import httpx
from pydantic import BaseModel, Field, validator

from ..core.config import config

logger = logging.getLogger(__name__)


class HotelSearchParams(BaseModel):
    """Parameters for hotel search."""
    
    location: str = Field(..., description="Hotel location (city, address, or landmark)")
    check_in_date: str = Field(..., description="Check-in date in YYYY-MM-DD format")
    check_out_date: str = Field(..., description="Check-out date in YYYY-MM-DD format")
    guests: int = Field(default=1, description="Number of guests", ge=1, le=10)
    rooms: int = Field(default=1, description="Number of rooms", ge=1, le=5)
    budget_min: Optional[float] = Field(None, description="Minimum budget per night")
    budget_max: Optional[float] = Field(None, description="Maximum budget per night")
    amenities: Optional[List[str]] = Field(None, description="Preferred amenities")
    
    @validator('check_in_date', 'check_out_date')
    def validate_date_format(cls, v):
        try:
            datetime.strptime(v, '%Y-%m-%d')
            return v
        except ValueError:
            raise ValueError('Date must be in YYYY-MM-DD format')
    
    @validator('check_out_date')
    def validate_checkout_after_checkin(cls, v, values):
        if 'check_in_date' in values:
            check_in = datetime.strptime(values['check_in_date'], '%Y-%m-%d')
            check_out = datetime.strptime(v, '%Y-%m-%d')
            if check_out <= check_in:
                raise ValueError('Check-out date must be after check-in date')
        return v


class FlightSearchParams(BaseModel):
    """Parameters for flight search."""
    
    origin: str = Field(..., description="Origin airport code or city")
    destination: str = Field(..., description="Destination airport code or city")
    departure_date: str = Field(..., description="Departure date in YYYY-MM-DD format")
    return_date: Optional[str] = Field(None, description="Return date in YYYY-MM-DD format (for round trip)")
    passengers: int = Field(default=1, description="Number of passengers", ge=1, le=9)
    cabin_class: str = Field(default="economy", description="Cabin class: economy, premium_economy, business, first")
    trip_type: str = Field(default="round_trip", description="Trip type: one_way, round_trip")
    budget_max: Optional[float] = Field(None, description="Maximum budget per person")
    
    @validator('departure_date', 'return_date')
    def validate_date_format(cls, v):
        if v is None:
            return v
        try:
            datetime.strptime(v, '%Y-%m-%d')
            return v
        except ValueError:
            raise ValueError('Date must be in YYYY-MM-DD format')
    
    @validator('cabin_class')
    def validate_cabin_class(cls, v):
        valid_classes = ["economy", "premium_economy", "business", "first"]
        if v.lower() not in valid_classes:
            raise ValueError(f'Cabin class must be one of: {", ".join(valid_classes)}')
        return v.lower()
    
    @validator('trip_type')
    def validate_trip_type(cls, v):
        valid_types = ["one_way", "round_trip"]
        if v.lower() not in valid_types:
            raise ValueError(f'Trip type must be one of: {", ".join(valid_types)}')
        return v.lower()


class TravelAPIClient:
    """Client for travel API integrations."""
    
    def __init__(self):
        self.config = config.travel_api
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def search_hotels(self, params: HotelSearchParams) -> Dict[str, Any]:
        """Search for hotels using the travel API."""
        try:
            headers = {}
            if self.config.api_key:
                headers["Authorization"] = f"Bearer {self.config.api_key}"
            
            # Convert params to API format
            api_params = {
                "location": params.location,
                "checkin": params.check_in_date,
                "checkout": params.check_out_date,
                "guests": params.guests,
                "rooms": params.rooms
            }
            
            if params.budget_min:
                api_params["price_min"] = params.budget_min
            if params.budget_max:
                api_params["price_max"] = params.budget_max
            if params.amenities:
                api_params["amenities"] = ",".join(params.amenities)
            
            # Make API request
            response = await self.client.get(
                self.config.hotel_search_url,
                params=api_params,
                headers=headers
            )
            response.raise_for_status()
            
            data = response.json()
            
            # Process and format the response
            return {
                "success": True,
                "hotels": data.get("hotels", []),
                "total_results": data.get("total", 0),
                "search_params": params.dict()
            }
            
        except httpx.HTTPStatusError as e:
            logger.error(f"Hotel search API error: {e.response.status_code} - {e.response.text}")
            return {
                "success": False,
                "error": f"API error: {e.response.status_code}",
                "message": "Failed to search hotels. Please try again later."
            }
        except Exception as e:
            logger.error(f"Hotel search error: {str(e)}")
            return {
                "success": False,
                "error": "search_failed",
                "message": f"Hotel search failed: {str(e)}"
            }
    
    async def search_flights(self, params: FlightSearchParams) -> Dict[str, Any]:
        """Search for flights using the travel API."""
        try:
            headers = {}
            if self.config.api_key:
                headers["Authorization"] = f"Bearer {self.config.api_key}"
            
            # Convert params to API format
            api_params = {
                "origin": params.origin,
                "destination": params.destination,
                "departure_date": params.departure_date,
                "passengers": params.passengers,
                "cabin_class": params.cabin_class,
                "trip_type": params.trip_type
            }
            
            if params.return_date:
                api_params["return_date"] = params.return_date
            if params.budget_max:
                api_params["max_price"] = params.budget_max
            
            # Make API request
            response = await self.client.get(
                self.config.flight_search_url,
                params=api_params,
                headers=headers
            )
            response.raise_for_status()
            
            data = response.json()
            
            # Process and format the response
            return {
                "success": True,
                "flights": data.get("flights", []),
                "total_results": data.get("total", 0),
                "search_params": params.dict()
            }
            
        except httpx.HTTPStatusError as e:
            logger.error(f"Flight search API error: {e.response.status_code} - {e.response.text}")
            return {
                "success": False,
                "error": f"API error: {e.response.status_code}",
                "message": "Failed to search flights. Please try again later."
            }
        except Exception as e:
            logger.error(f"Flight search error: {str(e)}")
            return {
                "success": False,
                "error": "search_failed",
                "message": f"Flight search failed: {str(e)}"
            }
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


# Global travel API client
travel_api_client = TravelAPIClient()

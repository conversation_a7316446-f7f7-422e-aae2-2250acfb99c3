"""MCP Server implementation for travel tools."""

import asyncio
import json
import logging
from typing import Dict, Any, List
from mcp.server import Server
from mcp.types import Tool, TextContent

from .tools import (
    TravelAPIClient, 
    HotelSearchParams, 
    FlightSearchParams,
    travel_api_client
)
from ..core.config import config

logger = logging.getLogger(__name__)


class TravelMCPServer:
    """MCP Server for travel search tools."""
    
    def __init__(self):
        self.server = Server("travel-search")
        self.travel_client = travel_api_client
        self._register_tools()
    
    def _register_tools(self):
        """Register all travel search tools."""
        
        # Hotel search tool
        @self.server.list_tools()
        async def list_tools() -> List[Tool]:
            return [
                Tool(
                    name="search_hotels",
                    description="Search for hotels based on location, dates, and preferences",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "Hotel location (city, address, or landmark)"
                            },
                            "check_in_date": {
                                "type": "string",
                                "description": "Check-in date in YYYY-MM-DD format"
                            },
                            "check_out_date": {
                                "type": "string",
                                "description": "Check-out date in YYYY-MM-DD format"
                            },
                            "guests": {
                                "type": "integer",
                                "description": "Number of guests",
                                "minimum": 1,
                                "maximum": 10,
                                "default": 1
                            },
                            "rooms": {
                                "type": "integer",
                                "description": "Number of rooms",
                                "minimum": 1,
                                "maximum": 5,
                                "default": 1
                            },
                            "budget_min": {
                                "type": "number",
                                "description": "Minimum budget per night"
                            },
                            "budget_max": {
                                "type": "number",
                                "description": "Maximum budget per night"
                            },
                            "amenities": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Preferred amenities"
                            }
                        },
                        "required": ["location", "check_in_date", "check_out_date"]
                    }
                ),
                Tool(
                    name="search_flights",
                    description="Search for flights based on origin, destination, dates, and preferences",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "origin": {
                                "type": "string",
                                "description": "Origin airport code or city"
                            },
                            "destination": {
                                "type": "string",
                                "description": "Destination airport code or city"
                            },
                            "departure_date": {
                                "type": "string",
                                "description": "Departure date in YYYY-MM-DD format"
                            },
                            "return_date": {
                                "type": "string",
                                "description": "Return date in YYYY-MM-DD format (for round trip)"
                            },
                            "passengers": {
                                "type": "integer",
                                "description": "Number of passengers",
                                "minimum": 1,
                                "maximum": 9,
                                "default": 1
                            },
                            "cabin_class": {
                                "type": "string",
                                "description": "Cabin class",
                                "enum": ["economy", "premium_economy", "business", "first"],
                                "default": "economy"
                            },
                            "trip_type": {
                                "type": "string",
                                "description": "Trip type",
                                "enum": ["one_way", "round_trip"],
                                "default": "round_trip"
                            },
                            "budget_max": {
                                "type": "number",
                                "description": "Maximum budget per person"
                            }
                        },
                        "required": ["origin", "destination", "departure_date"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls."""
            try:
                if name == "search_hotels":
                    return await self._handle_hotel_search(arguments)
                elif name == "search_flights":
                    return await self._handle_flight_search(arguments)
                else:
                    return [TextContent(
                        type="text",
                        text=f"Unknown tool: {name}"
                    )]
            except Exception as e:
                logger.error(f"Tool call error for {name}: {str(e)}")
                return [TextContent(
                    type="text",
                    text=f"Error executing {name}: {str(e)}"
                )]
    
    async def _handle_hotel_search(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Handle hotel search tool call."""
        try:
            # Validate parameters
            params = HotelSearchParams(**arguments)
            
            # Perform search
            result = await self.travel_client.search_hotels(params)
            
            # Format response
            if result["success"]:
                response_text = self._format_hotel_results(result)
            else:
                response_text = f"Hotel search failed: {result.get('message', 'Unknown error')}"
            
            return [TextContent(type="text", text=response_text)]
            
        except Exception as e:
            logger.error(f"Hotel search error: {str(e)}")
            return [TextContent(
                type="text",
                text=f"Hotel search failed: {str(e)}"
            )]
    
    async def _handle_flight_search(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """Handle flight search tool call."""
        try:
            # Validate parameters
            params = FlightSearchParams(**arguments)
            
            # Perform search
            result = await self.travel_client.search_flights(params)
            
            # Format response
            if result["success"]:
                response_text = self._format_flight_results(result)
            else:
                response_text = f"Flight search failed: {result.get('message', 'Unknown error')}"
            
            return [TextContent(type="text", text=response_text)]
            
        except Exception as e:
            logger.error(f"Flight search error: {str(e)}")
            return [TextContent(
                type="text",
                text=f"Flight search failed: {str(e)}"
            )]
    
    def _format_hotel_results(self, result: Dict[str, Any]) -> str:
        """Format hotel search results for display."""
        hotels = result.get("hotels", [])
        total = result.get("total_results", 0)
        
        if not hotels:
            return "No hotels found matching your criteria."
        
        formatted = f"Found {total} hotels:\n\n"
        
        for i, hotel in enumerate(hotels[:5], 1):  # Show top 5 results
            formatted += f"{i}. **{hotel.get('name', 'Unknown Hotel')}**\n"
            formatted += f"   Location: {hotel.get('address', 'N/A')}\n"
            formatted += f"   Price: ${hotel.get('price_per_night', 'N/A')}/night\n"
            formatted += f"   Rating: {hotel.get('rating', 'N/A')}/5\n"
            
            amenities = hotel.get('amenities', [])
            if amenities:
                formatted += f"   Amenities: {', '.join(amenities[:3])}\n"
            
            formatted += "\n"
        
        if total > 5:
            formatted += f"... and {total - 5} more hotels available.\n"
        
        return formatted
    
    def _format_flight_results(self, result: Dict[str, Any]) -> str:
        """Format flight search results for display."""
        flights = result.get("flights", [])
        total = result.get("total_results", 0)
        
        if not flights:
            return "No flights found matching your criteria."
        
        formatted = f"Found {total} flights:\n\n"
        
        for i, flight in enumerate(flights[:5], 1):  # Show top 5 results
            formatted += f"{i}. **{flight.get('airline', 'Unknown Airline')}** - {flight.get('flight_number', 'N/A')}\n"
            formatted += f"   Route: {flight.get('origin', 'N/A')} → {flight.get('destination', 'N/A')}\n"
            formatted += f"   Departure: {flight.get('departure_time', 'N/A')}\n"
            formatted += f"   Arrival: {flight.get('arrival_time', 'N/A')}\n"
            formatted += f"   Duration: {flight.get('duration', 'N/A')}\n"
            formatted += f"   Price: ${flight.get('price', 'N/A')}\n"
            formatted += f"   Stops: {flight.get('stops', 0)}\n\n"
        
        if total > 5:
            formatted += f"... and {total - 5} more flights available.\n"
        
        return formatted
    
    async def run(self):
        """Run the MCP server."""
        try:
            logger.info(f"Starting Travel MCP Server on {config.mcp.host}:{config.mcp.port}")
            await self.server.run()
        except Exception as e:
            logger.error(f"MCP Server error: {str(e)}")
            raise
    
    async def shutdown(self):
        """Shutdown the MCP server."""
        await self.travel_client.close()
        logger.info("Travel MCP Server shutdown complete")


# Global MCP server instance
mcp_server = TravelMCPServer()

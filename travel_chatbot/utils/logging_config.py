"""Logging configuration for the travel chatbot."""

import logging
import sys
from typing import Optional
import structlog
from rich.logging import <PERSON>Handler
from rich.console import Console

from ..core.config import config


def setup_logging(log_level: Optional[str] = None) -> None:
    """Set up structured logging for the application."""
    
    # Use config log level if not specified
    if log_level is None:
        log_level = config.app.log_level
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(message)s",
        datefmt="[%X]",
        handlers=[
            RichHandler(
                console=Console(stderr=True),
                show_time=True,
                show_path=True,
                markup=True,
                rich_tracebacks=True
            )
        ]
    )
    
    # Set specific logger levels
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("boto3").setLevel(logging.WARNING)
    logging.getLogger("botocore").setLevel(logging.WARNING)
    
    # Create application logger
    logger = structlog.get_logger("travel_chatbot")
    logger.info("Logging configured", log_level=log_level)


def get_logger(name: str) -> structlog.BoundLogger:
    """Get a structured logger for a specific module."""
    return structlog.get_logger(name)

"""Response formatting utilities for travel chatbot."""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from langchain_core.messages import SystemMessage, HumanMessage

from ..core.bedrock_client import bedrock_client

logger = logging.getLogger(__name__)


class ResponseFormatter:
    """Format and enhance chatbot responses using LLM."""
    
    def __init__(self):
        self.formatting_prompts = {
            "hotel_results": """
            Format the hotel search results in a conversational and helpful way.
            
            Search Results: {results}
            User Query Context: {context}
            
            Guidelines:
            - Present results in an engaging, conversational tone
            - Highlight the best options and explain why they're recommended
            - Include key details like price, rating, and standout features
            - Use bullet points or numbered lists for clarity
            - Add helpful tips or considerations
            - End with a question to continue the conversation
            
            Make the response feel personal and helpful, not robotic.
            """,
            
            "flight_results": """
            Format the flight search results in a conversational and helpful way.
            
            Search Results: {results}
            User Query Context: {context}
            
            Guidelines:
            - Present results in an engaging, conversational tone
            - Highlight the best options and explain the trade-offs
            - Include key details like price, duration, stops, and departure times
            - Group similar options or explain different categories
            - Add helpful travel tips or considerations
            - End with a question to continue the conversation
            
            Make the response feel personal and helpful, not robotic.
            """,
            
            "error_message": """
            Create a helpful and empathetic error message for the user.
            
            Error Context: {error}
            User Intent: {intent}
            
            Guidelines:
            - Be empathetic and understanding
            - Explain what went wrong in simple terms
            - Suggest specific next steps or alternatives
            - Maintain a positive, helpful tone
            - Offer to help in other ways
            
            Keep it concise but supportive.
            """,
            
            "clarification": """
            Ask for clarification from the user in a natural, conversational way.
            
            Missing Information: {missing_info}
            Current Context: {context}
            Search Type: {search_type}
            
            Guidelines:
            - Ask for missing information naturally
            - Explain why the information is needed
            - Provide examples or suggestions when helpful
            - Keep the tone friendly and conversational
            - Ask for 1-2 pieces of information at a time
            
            Make it feel like a natural conversation, not an interrogation.
            """
        }
    
    async def format_hotel_results(
        self, 
        results: Dict[str, Any], 
        context: Dict[str, Any]
    ) -> str:
        """Format hotel search results conversationally."""
        try:
            if not results.get("success"):
                return await self.format_error_message(
                    results.get("message", "Hotel search failed"),
                    "hotel_search"
                )
            
            hotels = results.get("hotels", [])
            if not hotels:
                return await self._format_no_results("hotels", context)
            
            # Prepare context for LLM
            format_context = {
                "results": results,
                "context": context,
                "hotel_count": len(hotels),
                "top_hotels": hotels[:3]  # Top 3 for detailed formatting
            }
            
            prompt = self.formatting_prompts["hotel_results"].format(**format_context)
            
            messages = [
                SystemMessage(content="You are a helpful travel assistant formatting hotel search results."),
                HumanMessage(content=prompt)
            ]
            
            response = await bedrock_client.invoke(messages)
            return response.content
            
        except Exception as e:
            logger.error(f"Hotel results formatting error: {str(e)}")
            return self._fallback_hotel_format(results)
    
    async def format_flight_results(
        self, 
        results: Dict[str, Any], 
        context: Dict[str, Any]
    ) -> str:
        """Format flight search results conversationally."""
        try:
            if not results.get("success"):
                return await self.format_error_message(
                    results.get("message", "Flight search failed"),
                    "flight_search"
                )
            
            flights = results.get("flights", [])
            if not flights:
                return await self._format_no_results("flights", context)
            
            # Prepare context for LLM
            format_context = {
                "results": results,
                "context": context,
                "flight_count": len(flights),
                "top_flights": flights[:3],  # Top 3 for detailed formatting
                "categories": results.get("categories", {})
            }
            
            prompt = self.formatting_prompts["flight_results"].format(**format_context)
            
            messages = [
                SystemMessage(content="You are a helpful travel assistant formatting flight search results."),
                HumanMessage(content=prompt)
            ]
            
            response = await bedrock_client.invoke(messages)
            return response.content
            
        except Exception as e:
            logger.error(f"Flight results formatting error: {str(e)}")
            return self._fallback_flight_format(results)
    
    async def format_error_message(self, error: str, intent: str) -> str:
        """Format error messages empathetically."""
        try:
            prompt = self.formatting_prompts["error_message"].format(
                error=error,
                intent=intent
            )
            
            messages = [
                SystemMessage(content="You are a helpful travel assistant handling errors gracefully."),
                HumanMessage(content=prompt)
            ]
            
            response = await bedrock_client.invoke(messages)
            return response.content
            
        except Exception as e:
            logger.error(f"Error message formatting error: {str(e)}")
            return f"I apologize, but I encountered an issue with your {intent.replace('_', ' ')}. Let me try to help you in another way. Could you please try rephrasing your request?"
    
    async def format_clarification_request(
        self, 
        missing_info: List[str], 
        context: Dict[str, Any], 
        search_type: str
    ) -> str:
        """Format requests for missing information."""
        try:
            prompt = self.formatting_prompts["clarification"].format(
                missing_info=missing_info,
                context=context,
                search_type=search_type
            )
            
            messages = [
                SystemMessage(content="You are a helpful travel assistant asking for clarification."),
                HumanMessage(content=prompt)
            ]
            
            response = await bedrock_client.invoke(messages)
            return response.content
            
        except Exception as e:
            logger.error(f"Clarification formatting error: {str(e)}")
            return self._fallback_clarification(missing_info, search_type)
    
    async def _format_no_results(self, search_type: str, context: Dict[str, Any]) -> str:
        """Format no results message."""
        try:
            no_results_prompt = f"""
            The user searched for {search_type} but no results were found.
            
            Search context: {context}
            
            Provide a helpful response that:
            - Acknowledges that no results were found
            - Suggests possible reasons (dates, location, budget, etc.)
            - Offers alternatives or suggestions to modify the search
            - Maintains a positive, helpful tone
            
            Be specific and actionable in your suggestions.
            """
            
            messages = [
                SystemMessage(content="You are a helpful travel assistant handling no results scenarios."),
                HumanMessage(content=no_results_prompt)
            ]
            
            response = await bedrock_client.invoke(messages)
            return response.content
            
        except Exception as e:
            logger.error(f"No results formatting error: {str(e)}")
            return f"I couldn't find any {search_type} matching your criteria. Would you like to try adjusting your search parameters, such as dates or budget?"
    
    def _fallback_hotel_format(self, results: Dict[str, Any]) -> str:
        """Fallback hotel formatting without LLM."""
        hotels = results.get("hotels", [])
        if not hotels:
            return "I couldn't find any hotels matching your criteria. Would you like to try different dates or location?"
        
        response = f"I found {len(hotels)} hotels for you:\n\n"
        
        for i, hotel in enumerate(hotels[:3], 1):
            response += f"{i}. **{hotel.get('name', 'Hotel')}**\n"
            response += f"   📍 {hotel.get('address', 'Location not specified')}\n"
            response += f"   💰 ${hotel.get('price_per_night', 'N/A')}/night\n"
            if hotel.get('rating'):
                response += f"   ⭐ {hotel.get('rating')}/5\n"
            response += "\n"
        
        if len(hotels) > 3:
            response += f"... and {len(hotels) - 3} more options available.\n\n"
        
        response += "Would you like more details about any of these hotels?"
        return response
    
    def _fallback_flight_format(self, results: Dict[str, Any]) -> str:
        """Fallback flight formatting without LLM."""
        flights = results.get("flights", [])
        if not flights:
            return "I couldn't find any flights matching your criteria. Would you like to try different dates or destinations?"
        
        response = f"I found {len(flights)} flights for you:\n\n"
        
        for i, flight in enumerate(flights[:3], 1):
            stops_text = "Direct" if flight.get('stops', 0) == 0 else f"{flight.get('stops')} stop(s)"
            response += f"{i}. **{flight.get('airline', 'Airline')} {flight.get('flight_number', '')}**\n"
            response += f"   🛫 {flight.get('departure_time', 'N/A')} → {flight.get('arrival_time', 'N/A')}\n"
            response += f"   ⏱️ {flight.get('duration', 'N/A')} ({stops_text})\n"
            response += f"   💰 ${flight.get('price', 'N/A')}\n\n"
        
        if len(flights) > 3:
            response += f"... and {len(flights) - 3} more options available.\n\n"
        
        response += "Would you like more details about any of these flights?"
        return response
    
    def _fallback_clarification(self, missing_info: List[str], search_type: str) -> str:
        """Fallback clarification without LLM."""
        search_name = search_type.replace('_', ' ')
        
        if len(missing_info) == 1:
            return f"To help you find the best {search_name} options, I need to know: {missing_info[0]}. Could you please provide this information?"
        else:
            info_list = ", ".join(missing_info[:-1]) + f", and {missing_info[-1]}"
            return f"To help you find the best {search_name} options, I need a few more details: {info_list}. Could you please provide this information?"


# Global response formatter instance
response_formatter = ResponseFormatter()

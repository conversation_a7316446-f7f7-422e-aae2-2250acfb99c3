"""Hotel search integration with enhanced functionality."""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, date
from pydantic import BaseModel, Field

from ..mcp_server.tools import HotelSearchParams, travel_api_client
from ..core.bedrock_client import bedrock_client
from langchain_core.messages import SystemMessage, HumanMessage

logger = logging.getLogger(__name__)


class HotelSearchResult(BaseModel):
    """Enhanced hotel search result with additional processing."""
    
    hotel_id: str
    name: str
    address: str
    city: str
    country: str
    price_per_night: float
    currency: str = "USD"
    rating: Optional[float] = None
    review_count: Optional[int] = None
    amenities: List[str] = Field(default_factory=list)
    images: List[str] = Field(default_factory=list)
    description: Optional[str] = None
    availability: bool = True
    distance_from_center: Optional[float] = None
    cancellation_policy: Optional[str] = None
    
    def get_summary(self) -> str:
        """Get a brief summary of the hotel."""
        summary = f"{self.name} - ${self.price_per_night}/night"
        if self.rating:
            summary += f" ({self.rating}/5)"
        return summary


class HotelSearchService:
    """Enhanced hotel search service with LLM-powered recommendations."""
    
    def __init__(self):
        self.api_client = travel_api_client
    
    async def search_hotels(
        self, 
        params: HotelSearchParams,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Search for hotels with enhanced processing."""
        try:
            # Perform the basic search
            raw_results = await self.api_client.search_hotels(params)
            
            if not raw_results.get("success"):
                return raw_results
            
            # Process and enhance results
            enhanced_results = await self._enhance_results(
                raw_results, 
                params, 
                user_preferences
            )
            
            return enhanced_results
            
        except Exception as e:
            logger.error(f"Hotel search service error: {str(e)}")
            return {
                "success": False,
                "error": "search_service_error",
                "message": f"Hotel search service failed: {str(e)}"
            }
    
    async def _enhance_results(
        self, 
        raw_results: Dict[str, Any], 
        params: HotelSearchParams,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Enhance search results with LLM-powered analysis."""
        
        hotels = raw_results.get("hotels", [])
        if not hotels:
            return raw_results
        
        # Convert to enhanced hotel objects
        enhanced_hotels = []
        for hotel_data in hotels:
            try:
                hotel = HotelSearchResult(**hotel_data)
                enhanced_hotels.append(hotel)
            except Exception as e:
                logger.warning(f"Failed to parse hotel data: {str(e)}")
                continue
        
        # Sort and rank hotels
        ranked_hotels = await self._rank_hotels(enhanced_hotels, params, user_preferences)
        
        # Generate recommendations
        recommendations = await self._generate_recommendations(ranked_hotels, params)
        
        return {
            "success": True,
            "hotels": [hotel.dict() for hotel in ranked_hotels],
            "total_results": len(ranked_hotels),
            "recommendations": recommendations,
            "search_params": params.dict(),
            "enhanced": True
        }
    
    async def _rank_hotels(
        self, 
        hotels: List[HotelSearchResult], 
        params: HotelSearchParams,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> List[HotelSearchResult]:
        """Rank hotels based on search criteria and preferences."""
        
        def calculate_score(hotel: HotelSearchResult) -> float:
            score = 0.0
            
            # Price score (lower price = higher score, within budget)
            if params.budget_max:
                if hotel.price_per_night <= params.budget_max:
                    price_ratio = hotel.price_per_night / params.budget_max
                    score += (1 - price_ratio) * 30  # Max 30 points for price
                else:
                    score -= 20  # Penalty for over budget
            
            # Rating score
            if hotel.rating:
                score += hotel.rating * 10  # Max 50 points for 5-star rating
            
            # Amenities score
            if params.amenities and hotel.amenities:
                matching_amenities = set(params.amenities) & set(hotel.amenities)
                score += len(matching_amenities) * 5  # 5 points per matching amenity
            
            # Distance score (closer to center = higher score)
            if hotel.distance_from_center is not None:
                if hotel.distance_from_center <= 2:  # Within 2km of center
                    score += 10
                elif hotel.distance_from_center <= 5:  # Within 5km
                    score += 5
            
            # Review count bonus
            if hotel.review_count and hotel.review_count > 100:
                score += 5
            
            return score
        
        # Calculate scores and sort
        for hotel in hotels:
            hotel.score = calculate_score(hotel)
        
        return sorted(hotels, key=lambda h: getattr(h, 'score', 0), reverse=True)
    
    async def _generate_recommendations(
        self, 
        hotels: List[HotelSearchResult], 
        params: HotelSearchParams
    ) -> Dict[str, Any]:
        """Generate LLM-powered recommendations."""
        
        if not hotels:
            return {"message": "No hotels found matching your criteria."}
        
        # Prepare data for LLM
        top_hotels = hotels[:3]  # Top 3 hotels
        hotel_summaries = [hotel.get_summary() for hotel in top_hotels]
        
        recommendation_prompt = f"""
        Based on the hotel search results, provide personalized recommendations for the user.
        
        Search criteria:
        - Location: {params.location}
        - Dates: {params.check_in_date} to {params.check_out_date}
        - Guests: {params.guests}
        - Budget: {params.budget_min or 'No minimum'} - {params.budget_max or 'No maximum'}
        
        Top hotels found:
        {chr(10).join(f"{i+1}. {summary}" for i, summary in enumerate(hotel_summaries))}
        
        Provide:
        1. A brief recommendation for the best overall choice
        2. Alternative suggestions for different priorities (budget, luxury, location)
        3. Any important considerations for the user
        
        Keep the response conversational and helpful.
        """
        
        try:
            messages = [
                SystemMessage(content="You are a travel expert providing hotel recommendations."),
                HumanMessage(content=recommendation_prompt)
            ]
            
            response = await bedrock_client.invoke(messages)
            
            return {
                "message": response.content,
                "top_choice": top_hotels[0].dict() if top_hotels else None,
                "alternatives": [hotel.dict() for hotel in top_hotels[1:3]]
            }
            
        except Exception as e:
            logger.error(f"Failed to generate recommendations: {str(e)}")
            return {
                "message": f"Found {len(hotels)} hotels. The top choice is {hotels[0].name} at ${hotels[0].price_per_night}/night.",
                "top_choice": hotels[0].dict(),
                "alternatives": [hotel.dict() for hotel in hotels[1:3]]
            }
    
    async def get_hotel_details(self, hotel_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific hotel."""
        # This would typically make an API call to get detailed hotel information
        # For now, we'll return a placeholder
        return {
            "hotel_id": hotel_id,
            "detailed_info": "Detailed hotel information would be fetched from the API",
            "booking_options": [],
            "policies": {},
            "nearby_attractions": []
        }
    
    async def check_availability(
        self, 
        hotel_id: str, 
        check_in: str, 
        check_out: str, 
        guests: int
    ) -> Dict[str, Any]:
        """Check real-time availability for a specific hotel."""
        # This would make a real-time availability check
        return {
            "hotel_id": hotel_id,
            "available": True,
            "rooms_available": 5,
            "current_price": 150.0,
            "last_updated": datetime.now().isoformat()
        }


# Global hotel search service instance
hotel_search_service = HotelSearchService()

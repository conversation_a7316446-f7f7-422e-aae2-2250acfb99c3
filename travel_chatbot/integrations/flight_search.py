"""Flight search integration with enhanced functionality."""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel, Field

from ..mcp_server.tools import FlightSearchParams, travel_api_client
from ..core.bedrock_client import bedrock_client
from langchain_core.messages import SystemMessage, HumanMessage

logger = logging.getLogger(__name__)


class FlightSearchResult(BaseModel):
    """Enhanced flight search result with additional processing."""
    
    flight_id: str
    airline: str
    flight_number: str
    origin: str
    destination: str
    departure_time: str
    arrival_time: str
    duration: str
    price: float
    currency: str = "USD"
    stops: int = 0
    aircraft_type: Optional[str] = None
    cabin_class: str = "economy"
    baggage_included: bool = True
    cancellation_allowed: bool = True
    change_allowed: bool = True
    
    def get_summary(self) -> str:
        """Get a brief summary of the flight."""
        stops_text = "Direct" if self.stops == 0 else f"{self.stops} stop{'s' if self.stops > 1 else ''}"
        return f"{self.airline} {self.flight_number} - ${self.price} ({stops_text})"
    
    def get_duration_minutes(self) -> int:
        """Convert duration string to minutes for comparison."""
        try:
            # Parse duration like "2h 30m" or "1h 45m"
            parts = self.duration.lower().replace('h', '').replace('m', '').split()
            if len(parts) == 2:
                return int(parts[0]) * 60 + int(parts[1])
            elif len(parts) == 1:
                if 'h' in self.duration:
                    return int(parts[0]) * 60
                else:
                    return int(parts[0])
        except:
            pass
        return 0


class FlightSearchService:
    """Enhanced flight search service with LLM-powered recommendations."""
    
    def __init__(self):
        self.api_client = travel_api_client
    
    async def search_flights(
        self, 
        params: FlightSearchParams,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Search for flights with enhanced processing."""
        try:
            # Perform the basic search
            raw_results = await self.api_client.search_flights(params)
            
            if not raw_results.get("success"):
                return raw_results
            
            # Process and enhance results
            enhanced_results = await self._enhance_results(
                raw_results, 
                params, 
                user_preferences
            )
            
            return enhanced_results
            
        except Exception as e:
            logger.error(f"Flight search service error: {str(e)}")
            return {
                "success": False,
                "error": "search_service_error",
                "message": f"Flight search service failed: {str(e)}"
            }
    
    async def _enhance_results(
        self, 
        raw_results: Dict[str, Any], 
        params: FlightSearchParams,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Enhance search results with LLM-powered analysis."""
        
        flights = raw_results.get("flights", [])
        if not flights:
            return raw_results
        
        # Convert to enhanced flight objects
        enhanced_flights = []
        for flight_data in flights:
            try:
                flight = FlightSearchResult(**flight_data)
                enhanced_flights.append(flight)
            except Exception as e:
                logger.warning(f"Failed to parse flight data: {str(e)}")
                continue
        
        # Sort and rank flights
        ranked_flights = await self._rank_flights(enhanced_flights, params, user_preferences)
        
        # Generate recommendations
        recommendations = await self._generate_recommendations(ranked_flights, params)
        
        # Group flights by categories
        categorized_flights = self._categorize_flights(ranked_flights)
        
        return {
            "success": True,
            "flights": [flight.dict() for flight in ranked_flights],
            "total_results": len(ranked_flights),
            "recommendations": recommendations,
            "categories": categorized_flights,
            "search_params": params.dict(),
            "enhanced": True
        }
    
    async def _rank_flights(
        self, 
        flights: List[FlightSearchResult], 
        params: FlightSearchParams,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> List[FlightSearchResult]:
        """Rank flights based on search criteria and preferences."""
        
        def calculate_score(flight: FlightSearchResult) -> float:
            score = 0.0
            
            # Price score (lower price = higher score, within budget)
            if params.budget_max:
                if flight.price <= params.budget_max:
                    price_ratio = flight.price / params.budget_max
                    score += (1 - price_ratio) * 40  # Max 40 points for price
                else:
                    score -= 30  # Penalty for over budget
            else:
                # If no budget specified, favor lower prices
                max_price = max(f.price for f in flights) if flights else flight.price
                if max_price > 0:
                    score += (1 - flight.price / max_price) * 20
            
            # Direct flight bonus
            if flight.stops == 0:
                score += 25  # Significant bonus for direct flights
            elif flight.stops == 1:
                score += 10  # Moderate bonus for one stop
            else:
                score -= flight.stops * 5  # Penalty for multiple stops
            
            # Duration score (shorter = better)
            duration_minutes = flight.get_duration_minutes()
            if duration_minutes > 0:
                # Normalize duration score (assume max reasonable duration is 24 hours)
                max_duration = 24 * 60
                duration_score = (1 - min(duration_minutes / max_duration, 1)) * 20
                score += duration_score
            
            # Departure time preferences (avoid very early or very late)
            try:
                dep_time = datetime.strptime(flight.departure_time, "%H:%M")
                hour = dep_time.hour
                if 6 <= hour <= 22:  # Reasonable hours
                    score += 10
                elif hour < 6 or hour > 22:  # Very early or late
                    score -= 5
            except:
                pass
            
            # Airline reputation (simplified - in real implementation, use airline ratings)
            major_airlines = ['American', 'Delta', 'United', 'Southwest', 'JetBlue']
            if any(airline in flight.airline for airline in major_airlines):
                score += 5
            
            # Flexibility bonuses
            if flight.cancellation_allowed:
                score += 3
            if flight.change_allowed:
                score += 3
            
            return score
        
        # Calculate scores and sort
        for flight in flights:
            flight.score = calculate_score(flight)
        
        return sorted(flights, key=lambda f: getattr(f, 'score', 0), reverse=True)
    
    def _categorize_flights(self, flights: List[FlightSearchResult]) -> Dict[str, List[Dict[str, Any]]]:
        """Categorize flights into different groups."""
        categories = {
            "best_value": [],
            "fastest": [],
            "cheapest": [],
            "direct": []
        }
        
        if not flights:
            return categories
        
        # Best value (top ranked)
        categories["best_value"] = [flights[0].dict()] if flights else []
        
        # Fastest flights
        fastest_flights = sorted(flights, key=lambda f: f.get_duration_minutes())
        categories["fastest"] = [fastest_flights[0].dict()] if fastest_flights else []
        
        # Cheapest flights
        cheapest_flights = sorted(flights, key=lambda f: f.price)
        categories["cheapest"] = [cheapest_flights[0].dict()] if cheapest_flights else []
        
        # Direct flights
        direct_flights = [f for f in flights if f.stops == 0]
        if direct_flights:
            categories["direct"] = [direct_flights[0].dict()]
        
        return categories
    
    async def _generate_recommendations(
        self, 
        flights: List[FlightSearchResult], 
        params: FlightSearchParams
    ) -> Dict[str, Any]:
        """Generate LLM-powered flight recommendations."""
        
        if not flights:
            return {"message": "No flights found matching your criteria."}
        
        # Prepare data for LLM
        top_flights = flights[:3]  # Top 3 flights
        flight_summaries = [flight.get_summary() for flight in top_flights]
        
        recommendation_prompt = f"""
        Based on the flight search results, provide personalized recommendations for the user.
        
        Search criteria:
        - Route: {params.origin} to {params.destination}
        - Departure: {params.departure_date}
        - Return: {params.return_date or 'One-way'}
        - Passengers: {params.passengers}
        - Class: {params.cabin_class}
        - Budget: Up to ${params.budget_max or 'No limit'}
        
        Top flights found:
        {chr(10).join(f"{i+1}. {summary}" for i, summary in enumerate(flight_summaries))}
        
        Provide:
        1. A brief recommendation for the best overall choice
        2. Alternative suggestions for different priorities (price, speed, convenience)
        3. Any important considerations (layovers, timing, etc.)
        
        Keep the response conversational and helpful.
        """
        
        try:
            messages = [
                SystemMessage(content="You are a travel expert providing flight recommendations."),
                HumanMessage(content=recommendation_prompt)
            ]
            
            response = await bedrock_client.invoke(messages)
            
            return {
                "message": response.content,
                "top_choice": top_flights[0].dict() if top_flights else None,
                "alternatives": [flight.dict() for flight in top_flights[1:3]]
            }
            
        except Exception as e:
            logger.error(f"Failed to generate recommendations: {str(e)}")
            return {
                "message": f"Found {len(flights)} flights. The top choice is {flights[0].airline} {flights[0].flight_number} at ${flights[0].price}.",
                "top_choice": flights[0].dict(),
                "alternatives": [flight.dict() for flight in flights[1:3]]
            }
    
    async def get_flight_details(self, flight_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific flight."""
        # This would typically make an API call to get detailed flight information
        return {
            "flight_id": flight_id,
            "detailed_info": "Detailed flight information would be fetched from the API",
            "seat_map": {},
            "baggage_policy": {},
            "meal_options": [],
            "entertainment": {}
        }
    
    async def check_price_alerts(
        self, 
        params: FlightSearchParams,
        target_price: float
    ) -> Dict[str, Any]:
        """Set up price alerts for flight searches."""
        return {
            "alert_id": f"alert_{datetime.now().timestamp()}",
            "route": f"{params.origin} to {params.destination}",
            "target_price": target_price,
            "current_lowest": 0,  # Would be fetched from API
            "alert_active": True
        }


# Global flight search service instance
flight_search_service = FlightSearchService()

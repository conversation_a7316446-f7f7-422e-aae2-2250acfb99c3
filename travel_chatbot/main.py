"""Main application entry point for the travel chatbot."""

import asyncio
import sys
import uuid
from typing import Optional
import click
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.prompt import Prompt

from .core.config import config
from .utils.logging_config import setup_logging, get_logger
from .conversation.graph import conversation_graph
from .core.bedrock_client import bedrock_client

console = Console()
logger = get_logger(__name__)


class TravelChatbot:
    """Main travel chatbot application."""
    
    def __init__(self):
        self.session_id = str(uuid.uuid4())
        self.conversation_graph = conversation_graph
        
    async def start_conversation(self):
        """Start an interactive conversation with the user."""
        console.print(Panel.fit(
            "[bold blue]🌍 Travel Assistant[/bold blue]\n"
            "I'm here to help you find hotels and flights!\n"
            "Type 'quit' or 'exit' to end the conversation.",
            title="Welcome",
            border_style="blue"
        ))
        
        # Test Bedrock connection
        if not await self._test_connections():
            console.print("[red]❌ Connection test failed. Please check your configuration.[/red]")
            return
        
        console.print("[green]✅ Ready to help with your travel plans![/green]\n")
        
        while True:
            try:
                # Get user input
                user_input = Prompt.ask("\n[bold cyan]You[/bold cyan]")
                
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    console.print("[yellow]👋 Thanks for using Travel Assistant! Have a great trip![/yellow]")
                    break
                
                if not user_input.strip():
                    continue
                
                # Show thinking indicator
                with console.status("[bold green]Thinking..."):
                    response = await self.conversation_graph.process_message(
                        self.session_id, 
                        user_input
                    )
                
                # Display response
                self._display_response(response)
                
            except KeyboardInterrupt:
                console.print("\n[yellow]👋 Goodbye![/yellow]")
                break
            except Exception as e:
                logger.error("Conversation error", error=str(e))
                console.print(f"[red]❌ Sorry, I encountered an error: {str(e)}[/red]")
    
    async def _test_connections(self) -> bool:
        """Test all necessary connections."""
        try:
            # Test Bedrock connection
            console.print("🔍 Testing AWS Bedrock connection...")
            if not bedrock_client.test_connection():
                console.print("[red]❌ Bedrock connection failed[/red]")
                return False
            
            console.print("[green]✅ Bedrock connection successful[/green]")
            
            # Test MCP server (if running)
            # This would test the MCP server connection
            console.print("🔍 MCP server ready for API calls...")
            
            return True
            
        except Exception as e:
            logger.error("Connection test failed", error=str(e))
            console.print(f"[red]❌ Connection test failed: {str(e)}[/red]")
            return False
    
    def _display_response(self, response: str):
        """Display the chatbot response with nice formatting."""
        # Create a panel for the response
        response_panel = Panel(
            response,
            title="[bold green]🤖 Travel Assistant[/bold green]",
            border_style="green",
            padding=(1, 2)
        )
        
        console.print(response_panel)


@click.group()
@click.option('--debug', is_flag=True, help='Enable debug logging')
@click.option('--log-level', default=None, help='Set log level (DEBUG, INFO, WARNING, ERROR)')
def cli(debug: bool, log_level: Optional[str]):
    """Travel Chatbot - AI-powered hotel and flight search assistant."""
    # Set up logging
    if debug:
        log_level = 'DEBUG'
    elif log_level is None:
        log_level = config.app.log_level
    
    setup_logging(log_level)
    
    # Validate configuration
    if not config.validate():
        console.print("[red]❌ Configuration validation failed. Please check your .env file.[/red]")
        sys.exit(1)


@cli.command()
def chat():
    """Start an interactive chat session."""
    chatbot = TravelChatbot()
    asyncio.run(chatbot.start_conversation())


@cli.command()
@click.option('--message', '-m', required=True, help='Single message to process')
@click.option('--session-id', default=None, help='Session ID for conversation continuity')
def single(message: str, session_id: Optional[str]):
    """Process a single message (useful for testing)."""
    if session_id is None:
        session_id = str(uuid.uuid4())
    
    async def process_single_message():
        try:
            response = await conversation_graph.process_message(session_id, message)
            console.print(Panel(
                response,
                title="[bold green]Response[/bold green]",
                border_style="green"
            ))
        except Exception as e:
            console.print(f"[red]Error: {str(e)}[/red]")
    
    asyncio.run(process_single_message())


@cli.command()
def test():
    """Test the chatbot configuration and connections."""
    async def run_tests():
        console.print("[bold blue]🧪 Running Travel Chatbot Tests[/bold blue]\n")
        
        # Test configuration
        console.print("1. Testing configuration...")
        if config.validate():
            console.print("[green]✅ Configuration valid[/green]")
        else:
            console.print("[red]❌ Configuration invalid[/red]")
            return
        
        # Test Bedrock connection
        console.print("2. Testing Bedrock connection...")
        try:
            if bedrock_client.test_connection():
                console.print("[green]✅ Bedrock connection successful[/green]")
            else:
                console.print("[red]❌ Bedrock connection failed[/red]")
        except Exception as e:
            console.print(f"[red]❌ Bedrock error: {str(e)}[/red]")
        
        # Test conversation flow
        console.print("3. Testing conversation flow...")
        try:
            test_session = str(uuid.uuid4())
            response = await conversation_graph.process_message(
                test_session, 
                "Hello, I need help finding a hotel"
            )
            if response:
                console.print("[green]✅ Conversation flow working[/green]")
                console.print(f"Sample response: {response[:100]}...")
            else:
                console.print("[red]❌ Conversation flow failed[/red]")
        except Exception as e:
            console.print(f"[red]❌ Conversation error: {str(e)}[/red]")
        
        console.print("\n[bold green]🎉 Test complete![/bold green]")
    
    asyncio.run(run_tests())


@cli.command()
def info():
    """Show chatbot information and configuration."""
    console.print(Panel.fit(
        f"[bold blue]Travel Chatbot Information[/bold blue]\n\n"
        f"[bold]Bedrock Configuration:[/bold]\n"
        f"  Model: {config.bedrock.model_id}\n"
        f"  Region: {config.bedrock.region}\n"
        f"  Max Tokens: {config.bedrock.max_tokens}\n"
        f"  Temperature: {config.bedrock.temperature}\n\n"
        f"[bold]MCP Configuration:[/bold]\n"
        f"  Host: {config.mcp.host}\n"
        f"  Port: {config.mcp.port}\n\n"
        f"[bold]Application:[/bold]\n"
        f"  Log Level: {config.app.log_level}\n"
        f"  Debug: {config.app.debug}",
        title="Configuration",
        border_style="blue"
    ))


def main():
    """Main entry point."""
    try:
        cli()
    except KeyboardInterrupt:
        console.print("\n[yellow]👋 Goodbye![/yellow]")
    except Exception as e:
        console.print(f"[red]❌ Application error: {str(e)}[/red]")
        sys.exit(1)


if __name__ == "__main__":
    main()

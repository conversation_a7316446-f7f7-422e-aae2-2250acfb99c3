# Core LLM and Conversation Management
langchain>=0.1.0
langchain-community>=0.0.20
langchain-aws>=0.1.0
langgraph>=0.0.40
langsmith>=0.0.80

# AWS Bedrock Integration
boto3>=1.34.0
botocore>=1.34.0

# MCP (Model Context Protocol)
mcp>=0.4.0
httpx>=0.25.0
pydantic>=2.5.0

# API and HTTP clients
requests>=2.31.0
aiohttp>=3.9.0

# Data processing and utilities
python-dotenv>=1.0.0
pyyaml>=6.0
click>=8.1.0
rich>=13.0.0
asyncio-mqtt>=0.13.0

# Date and time handling
python-dateutil>=2.8.0

# Logging and monitoring
structlog>=23.0.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.7.0

# Optional: For enhanced conversation capabilities
tiktoken>=0.5.0

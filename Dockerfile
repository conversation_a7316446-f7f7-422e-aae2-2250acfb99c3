FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
COPY pyproject.toml .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY travel_chatbot/ ./travel_chatbot/
COPY tests/ ./tests/
COPY README.md .
COPY .env.example .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app
RUN chown -R app:app /app
USER app

# Set Python path
ENV PYTHONPATH=/app

# Default command
CMD ["python", "-m", "travel_chatbot.main", "chat"]

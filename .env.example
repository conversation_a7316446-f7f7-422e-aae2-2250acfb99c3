# AWS Configuration for Bedrock
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here

# Bedrock Model Configuration
BEDROCK_MODEL_ID=anthropic.claude-3-5-sonnet-20241022-v2:0
BEDROCK_MAX_TOKENS=4096
BEDROCK_TEMPERATURE=0.7

# Travel API Configuration (Replace with your actual API endpoints)
HOTEL_SEARCH_API_URL=https://api.example.com/hotels/search
FLIGHT_SEARCH_API_URL=https://api.example.com/flights/search
TRAVEL_API_KEY=your_travel_api_key_here

# MCP Server Configuration
MCP_SERVER_HOST=localhost
MCP_SERVER_PORT=8000

# Application Configuration
LOG_LEVEL=INFO
DEBUG=false

# LangSmith (Optional - for monitoring and debugging)
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
LANGCHAIN_API_KEY=your_langsmith_api_key_here
LANGCHAIN_PROJECT=travel-chatbot

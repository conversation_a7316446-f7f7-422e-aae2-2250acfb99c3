[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "travel-chatbot"
version = "0.1.0"
description = "LLM-powered chatbot for hotel and flight search using Claude Sonnet 4"
authors = [
    {name = "Travel Chatbot Team"}
]
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "langchain>=0.1.0",
    "langchain-community>=0.0.20",
    "langchain-aws>=0.1.0",
    "langgraph>=0.0.40",
    "boto3>=1.34.0",
    "mcp>=0.4.0",
    "httpx>=0.25.0",
    "pydantic>=2.5.0",
    "requests>=2.31.0",
    "aiohttp>=3.9.0",
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0",
    "click>=8.1.0",
    "rich>=13.0.0",
    "python-dateutil>=2.8.0",
    "structlog>=23.0.0",
    "tiktoken>=0.5.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.7.0",
]

[project.scripts]
travel-chatbot = "travel_chatbot.main:main"

[tool.black]
line-length = 88
target-version = ['py39']

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"

version: '3.8'

services:
  travel-chatbot:
    build: .
    container_name: travel-chatbot
    environment:
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - BEDROCK_MODEL_ID=${BEDROCK_MODEL_ID:-anthropic.claude-3-5-sonnet-20241022-v2:0}
      - HOTEL_SEARCH_API_URL=${HOTEL_SEARCH_API_URL}
      - FLIGHT_SEARCH_API_URL=${FLIGHT_SEARCH_API_URL}
      - TRAVEL_API_KEY=${TRAVEL_API_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - ./.env:/app/.env:ro
    ports:
      - "8000:8000"
    command: python -m travel_chatbot.main chat
    restart: unless-stopped

  mcp-server:
    build: .
    container_name: travel-mcp-server
    environment:
      - AWS_REGION=${AWS_REGION:-us-east-1}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - HOTEL_SEARCH_API_URL=${HOTEL_SEARCH_API_URL}
      - FLIGHT_SEARCH_API_URL=${FLIGHT_SEARCH_API_URL}
      - TRAVEL_API_KEY=${TRAVEL_API_KEY}
      - MCP_SERVER_HOST=0.0.0.0
      - MCP_SERVER_PORT=8001
    volumes:
      - ./.env:/app/.env:ro
    ports:
      - "8001:8001"
    command: python -m travel_chatbot.mcp_server.server
    restart: unless-stopped

# Travel Chatbot

An intelligent LLM-powered chatbot for hotel and flight search using Claude Sonnet 4 via AWS Bedrock.

## Features

- **Conversational AI**: Natural language interaction for travel planning
- **Hotel Search**: Comprehensive hotel search with location, dates, and preferences
- **Flight Search**: Flight search with origin, destination, dates, and passenger details
- **Claude Sonnet 4**: Powered by <PERSON>thropic's <PERSON> 4 via AWS Bedrock
- **LangGraph**: Advanced conversation flow management
- **MCP Integration**: Model Context Protocol for seamless API integrations

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   User Input    │───▶│   LangGraph      │───▶│  Claude Sonnet  │
│                 │    │  Conversation    │    │   4 (Bedrock)   │
└─────────────────┘    │     Flow         │    └─────────────────┘
                       └──────────────────┘             │
                                │                       │
                                ▼                       ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   MCP Server     │───▶│  Travel APIs    │
                       │                  │    │ (Hotels/Flights)│
                       └──────────────────┘    └─────────────────┘
```

## Setup

1. **Clone and Install Dependencies**:
   ```bash
   git clone <repository-url>
   cd chatAssistance
   pip install -r requirements.txt
   ```

2. **Configure Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your AWS credentials and API keys
   ```

3. **AWS Bedrock Setup**:
   - Ensure you have AWS credentials configured
   - Enable Claude Sonnet 4 model access in AWS Bedrock console
   - Set appropriate IAM permissions for Bedrock access

## Usage

### Command Line Interface
```bash
# Start the chatbot
python -m travel_chatbot.main

# Or using the installed script
travel-chatbot
```

### Example Conversation
```
User: I need to find a hotel in New York for next weekend
Bot: I'd be happy to help you find a hotel in New York! To provide the best recommendations, I need a few more details:

1. What are your specific check-in and check-out dates?
2. How many guests will be staying?
3. Do you have any preferences for location within New York?
4. What's your budget range per night?

User: Check-in December 15th, check-out December 17th, 2 guests, prefer Manhattan, budget around $200-300 per night
Bot: Perfect! Let me search for hotels in Manhattan for December 15-17 for 2 guests in your budget range...

[Bot searches and presents results]
```

## Development

### Project Structure
```
travel_chatbot/
├── core/              # Core application logic
├── conversation/      # LangGraph conversation flows
├── integrations/      # API integrations
├── mcp_server/        # MCP server implementation
├── utils/             # Utility functions
└── main.py           # Application entry point

tests/
├── unit/             # Unit tests
└── integration/      # Integration tests
```

### Running Tests
```bash
pytest tests/
```

### Code Formatting
```bash
black travel_chatbot/
flake8 travel_chatbot/
```

## Configuration

Key environment variables:
- `AWS_REGION`: AWS region for Bedrock
- `BEDROCK_MODEL_ID`: Claude model identifier
- `HOTEL_SEARCH_API_URL`: Hotel search API endpoint
- `FLIGHT_SEARCH_API_URL`: Flight search API endpoint
- `TRAVEL_API_KEY`: API key for travel services

## License

MIT License - see LICENSE file for details.
